Here is a **high-detail technical plan** to implement a **centralized Parameter Workflow System** in a VB.NET WinForms application using DevExpress, designed for delegation to an AI agent or developer.

---

## 🧩 GOAL

Create a **centralized system** that:

* Loads **global app parameters** from a `Parameters` database table at **application startup**.
* Caches them in-memory for instant access without repeated DB queries.
* Allows all forms and components to **retrieve parameter values using a shared method**.
* Ensures maintainability, scalability, and single-responsibility across files.

---

## 📁 FILE STRUCTURE

| File                             | Type              | Responsibility                                                                                         |
| -------------------------------- | ----------------- | ------------------------------------------------------------------------------------------------------ |
| `ParameterManager.vb`            | Class (Singleton) | Loads and caches parameters at startup, provides global access methods.                                |
| `ParameterEntry.vb` *(optional)* | Data Model        | Represents a parameter entry with `Code` and `Value`. Used if parameters are shown in UI (e.g., grid). |
| `Program.vb`                     | Entry Point       | Triggers parameter loading before showing any UI.                                                      |
| `Database Table: Parameters`     | Table             | Stores all parameter key-value pairs.                                                                  |

---

## 🗃️ DATABASE DESIGN

Table: `Parameters`

| Column Name  | Type           | Description                                             |
| ------------ | -------------- | ------------------------------------------------------- |
| `ParamCode`  | `VARCHAR(50)`  | Unique parameter key (e.g., `"CURRENCY"`, `"DECIMALS"`) |
| `ParamValue` | `VARCHAR(255)` | Value of the parameter                                  |

* `ParamCode` should be `PRIMARY KEY` or `UNIQUE`.
* Case-insensitive lookup is preferred.

---

## 🔧 IMPLEMENTATION PLAN

---

### 1. `ParameterManager.vb` (Core Engine)

**Role:** Load once from DB, serve everywhere.

```vbnet
Public Class ParameterManager
    Private Shared _parameters As Dictionary(Of String, String)

    ' Load all parameters from the DB once
    Public Shared Sub LoadParameters()
        _parameters = New Dictionary(Of String, String)(StringComparer.OrdinalIgnoreCase)
        
        ' Your DB access function here
        Dim dt As DataTable = GetDataTable("SELECT ParamCode, ParamValue FROM Parameters")
        
        For Each row As DataRow In dt.Rows
            _parameters(row("ParamCode").ToString()) = row("ParamValue").ToString()
        Next
    End Sub

    ' Retrieve parameter value
    Public Shared Function GetValue(code As String, Optional defaultValue As String = "") As String
        If _parameters.ContainsKey(code) Then
            Return _parameters(code)
        End If
        Return defaultValue
    End Function

    ' Optional: Typed Getters
    Public Shared Function GetInt(code As String, Optional defaultValue As Integer = 0) As Integer
        Dim val = GetValue(code)
        Return If(Integer.TryParse(val, Nothing), Integer.Parse(val), defaultValue)
    End Function

    Public Shared Function GetBool(code As String, Optional defaultValue As Boolean = False) As Boolean
        Dim val = GetValue(code)
        Return If(Boolean.TryParse(val, Nothing), Boolean.Parse(val), defaultValue)
    End Function
End Class
```

**Key features:**

* Dictionary-based lookup (fast).
* Case-insensitive keys.
* Prevents repeated DB calls.
* Safe default fallbacks.

---

### 2. `Program.vb` (Startup Integration)

**Where:** Inside `Sub Main()`

```vbnet
Sub Main()
    ' Load global parameters before anything
    ParameterManager.LoadParameters()

    ' Launch the login form or main app
    Application.Run(New frmLogin())
End Sub
```

> This ensures all parameters are available **before any form is opened**.

---

### 3. Usage in Forms

**Every form can now do:**

```vbnet
Dim currency As String = ParameterManager.GetValue("CURRENCY", "AED")
Dim decimals As Integer = ParameterManager.GetInt("DECIMALS", 2)
```

**Example:**

```vbnet
lblCurrency.Text = ParameterManager.GetValue("CURRENCY", "AED")
gridView.Columns("Price").DisplayFormat.FormatString = "N" & ParameterManager.GetInt("DECIMALS", 2)
```

---

### 4. (Optional) `ParameterEntry.vb`

```vbnet
Public Class ParameterEntry
    Public Property Code As String
    Public Property Value As String
End Class
```

* Needed only if you display/edit parameters in a DevExpress grid or list.

---

## 🧠 AGENT TASK SUMMARY

### ✅ Create:

1. `ParameterManager.vb` – core logic
2. `ParameterEntry.vb` – optional model
3. Edit `Program.vb` to call `ParameterManager.LoadParameters()` before any form loads.

### ✅ Logic to Implement:

* Pull all `ParamCode`/`ParamValue` pairs from DB.
* Store in a **case-insensitive in-memory dictionary**.
* Provide a **safe, shared function** to retrieve values.
* **Fail-safe fallback** (default value if missing).
* Optional typed versions like `GetInt`, `GetBool`.

### ✅ Ensure:

* **No form should query DB for parameters again**.
* **All forms** read from `ParameterManager.GetValue("CODE")`.

---

## 🔄 Future-Proofing

* Later add:

  * `ReloadParameters()` if runtime updates are needed.
  * Write-back to DB if user changes parameters from UI.
  * Sync with config files or API if needed.

---

Let me know if you also want the same for **User Permissions**, following a similar structure.
